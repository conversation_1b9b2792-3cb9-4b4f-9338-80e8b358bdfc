<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Users</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h2 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .nav-links {
            text-align: center;
            margin-top: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .nav-links a:hover {
            background-color: #45a049;
        }
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Registered Users</h2>
        
        <?php
        $servername = "localhost";
        $username = "root";
        $password = "";
        $dbname = "test";
        
        try {
            $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if created_at column exists
            $hasCreatedAt = false;
            try {
                $pdo->query("SELECT created_at FROM user LIMIT 1");
                $hasCreatedAt = true;
            } catch(PDOException $e) {
                // Column doesn't exist, that's fine
            }

            // Query based on whether created_at exists
            if ($hasCreatedAt) {
                $stmt = $pdo->query("SELECT * FROM user ORDER BY created_at DESC");
            } else {
                $stmt = $pdo->query("SELECT * FROM user ORDER BY id");
            }
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (count($users) > 0) {
                echo "<table>";
                if ($hasCreatedAt) {
                    echo "<tr><th>ID</th><th>Name</th><th>Created At</th></tr>";
                } else {
                    echo "<tr><th>ID</th><th>Name</th></tr>";
                }

                foreach ($users as $user) {
                    echo "<tr>";
                    echo "<td>" . (isset($user['id']) ? htmlspecialchars($user['id']) : 'N/A') . "</td>";
                    echo "<td>" . (isset($user['name']) ? htmlspecialchars($user['name']) : 'N/A') . "</td>";
                    if ($hasCreatedAt) {
                        echo "<td>" . (isset($user['created_at']) ? htmlspecialchars($user['created_at']) : '-') . "</td>";
                    }
                    echo "</tr>";
                }

                echo "</table>";
                echo "<p><strong>Total users: " . count($users) . "</strong></p>";

                // Debug: Show what columns are available
                if (!empty($users)) {
                    echo "<div style='margin-top: 20px; padding: 10px; background-color: #f0f0f0; border-radius: 5px;'>";
                    echo "<strong>Debug - Available columns:</strong> " . implode(', ', array_keys($users[0]));
                    echo "</div>";
                }
            } else {
                echo "<div class='no-data'>No users found in the database.</div>";
            }
            
        } catch(PDOException $e) {
            echo "<div style='color: red;'>Error: " . $e->getMessage() . "</div>";
        }
        ?>
        
        <div class="nav-links">
            <a href="index.php">Add New User</a>
            <a href="setup_database.php">Setup Database</a>
        </div>
    </div>
</body>
</html>
