<?php
// Database setup script
// Run this file once to create the database table if it doesn't exist

$servername = "localhost";
$username = "root";  // Default XAMPP MySQL username
$password = "";      // Default XAMPP MySQL password (empty)
$dbname = "test";    // Your database name

try {
    // Create connection
    $pdo = new PDO("mysql:host=$servername", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $dbname");
    echo "Database '$dbname' created successfully or already exists.<br>";
    
    // Select the database
    $pdo->exec("USE $dbname");
    
    // Create table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS user (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "Table 'user' created successfully or already exists.<br>";
    
    echo "<br><strong>Database setup completed!</strong><br>";
    echo "You can now use the form to add users to your database.<br>";
    echo "<a href='index.html'>Go to Registration Form</a><br>";
    echo "<a href='view_users.php'>View All Users</a>";
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
