<?php
// Database setup script
// Run this file once to create the database table if it doesn't exist

$servername = "localhost";
$username = "root";  // Default XAMPP MySQL username
$password = "";      // Default XAMPP MySQL password (empty)
$dbname = "test";    // Your database name

try {
    // Create connection
    $pdo = new PDO("mysql:host=$servername", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $dbname");
    echo "Database '$dbname' created successfully or already exists.<br>";
    
    // Select the database
    $pdo->exec("USE $dbname");
    
    // Create table if it doesn't exist (matching your existing structure with capital column names)
    $sql = "CREATE TABLE IF NOT EXISTS user (
        ID VARCHAR(50) PRIMARY KEY,
        Name VARCHAR(100) NOT NULL
    )";

    $pdo->exec($sql);
    echo "Table 'user' created successfully or already exists.<br>";

    // Check if created_at column exists, if not add it
    try {
        $pdo->query("SELECT created_at FROM user LIMIT 1");
        echo "Column 'created_at' already exists.<br>";
    } catch(PDOException $e) {
        // Column doesn't exist, add it
        try {
            $pdo->exec("ALTER TABLE user ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            echo "Column 'created_at' added successfully.<br>";
        } catch(PDOException $e2) {
            echo "Note: Could not add 'created_at' column. Table will work without it.<br>";
        }
    }
    
    echo "<br><strong>Database setup completed!</strong><br>";
    echo "You can now use the form to add users to your database.<br>";
    echo "<a href='index.php'>Go to Registration Form</a><br>";
    echo "<a href='view_users.php'>View All Users</a>";
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
