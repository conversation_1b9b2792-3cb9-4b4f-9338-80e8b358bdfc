<?php
// Database configuration
$servername = "localhost";
$username = "root";  // Default XAMPP MySQL username
$password = "";      // Default XAMPP MySQL password (empty)
$dbname = "test";    // Your database name

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $name = trim($_POST['name']);
    $id = trim($_POST['id']);
    
    // Validate input
    if (empty($name) || empty($id)) {
        header("Location: index.php?error=Please fill in all fields");
        exit();
    }
    
    try {
        // Create connection using PDO
        $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if ID already exists (using correct column name)
        $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM user WHERE ID = ?");
        $checkStmt->execute([$id]);
        $count = $checkStmt->fetchColumn();

        if ($count > 0) {
            header("Location: index.php?error=ID already exists");
            exit();
        }

        // Prepare and execute insert statement (using correct column names)
        $stmt = $pdo->prepare("INSERT INTO user (Name, ID) VALUES (?, ?)");
        $stmt->execute([$name, $id]);
        
        // Redirect with success message
        header("Location: index.php?success=1");
        exit();
        
    } catch(PDOException $e) {
        // Handle database errors
        $error_message = "Database error: " . $e->getMessage();
        header("Location: index.php?error=" . urlencode($error_message));
        exit();
    }
} else {
    // If not POST request, redirect to form
    header("Location: index.php");
    exit();
}
?>
